// Fill out your copyright notice in the Description page of Project Settings.


#include "Net/UnrealNetwork.h"
#include "MyPlayerState.h"

AMyPlayerState::AMyPlayerState()
{
    // ...existing constructor code...
}

void AMyPlayerState::OnRep_Info()
{
    OnPlayerStateUpdated();
}

void AMyPlayerState::OnRep_Team()
{
    OnPlayerStateUpdated();
}

void AMyPlayerState::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(AMyPlayerState, Info);
    DOREPLIFETIME(AMyPlayerState, Team);
}

// Interface implementations
void AMyPlayerState::SetPlayerInfo(const FPlayerInfo& NewInfo)
{
    if (HasAuthority())
    {
        Info = NewInfo;
        OnRep_Info(); // Manually call for server
    }
}

FPlayerInfo AMyPlayerState::GetPlayerInfo() const
{
    return Info;
}

void AMyPlayerState::SetPlayerTeam(EPlayerTeam NewTeam)
{
    if (HasAuthority())
    {
        Team = NewTeam;
        OnRep_Team(); // Manually call for server
    }
}

EPlayerTeam AMyPlayerState::GetPlayerTeam() const
{
    return Team;
}

void AMyPlayerState::UpdatePlayerState(const FPlayerInfo& NewInfo, EPlayerTeam NewTeam)
{
    if (HasAuthority())
    {
        Info = NewInfo;
        Team = NewTeam;
        OnPlayerStateUpdated(); // Call the blueprint event
    }
}
