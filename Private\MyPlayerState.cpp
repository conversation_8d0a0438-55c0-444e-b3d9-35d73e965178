// Fill out your copyright notice in the Description page of Project Settings.


#include "Net/UnrealNetwork.h"
#include "MyPlayerState.h"

AMyPlayerState::AMyPlayerState()
{
    // ...existing constructor code...
}

void AMyPlayerState::OnRep_Info()
{
    OnPlayerStateUpdated();
}

void AMyPlayerState::OnRep_Team()
{
    OnPlayerStateUpdated();
}

void AMyPlayerState::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(AMyPlayerState, Info);
    DOREPLIFETIME(AMyPlayerState, Team);
}
