// Fill out your copyright notice in the Description page of Project Settings.


#include "Net/UnrealNetwork.h"
#include "MyPlayerState.h"

AMyPlayerState::AMyPlayerState()
{
    // ...existing constructor code...
}

void AMyPlayerState::OnRep_Info()
{
    OnPlayerStateUpdated();
}

void AMyPlayerState::OnRep_Team()
{
    OnPlayerStateUpdated();
}

void AMyPlayerState::GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const
{
    Super::GetLifetimeReplicatedProps(OutLifetimeProps);

    DOREPLIFETIME(AMyPlayerState, Info);
    DOREPLIFETIME(AMyPlayerState, Team);
}

// Interface implementations
void AMyPlayerState::SetPlayerInfo(const FPlayerInfo& NewInfo)
{
    if (HasAuthority())
    {
        Info = NewInfo;
        OnRep_Info(); // Manually call for server
    }
}

FPlayerInfo AMyPlayerState::GetPlayerInfo() const
{
    return Info;
}

void AMyPlayerState::SetPlayerTeam(EPlayerTeam NewTeam)
{
    if (HasAuthority())
    {
        Team = NewTeam;
        OnRep_Team(); // Manually call for server
    }
}

EPlayerTeam AMyPlayerState::GetPlayerTeam() const
{
    return Team;
}

// Modular Player Info property implementations
void AMyPlayerState::SetPlayerName(const FText& NewName)
{
    if (HasAuthority())
    {
        Info.Name = NewName;
        OnRep_Info(); // Manually call for server
    }
}

FText AMyPlayerState::GetPlayerName() const
{
    return Info.Name;
}

void AMyPlayerState::SetPlayerAvatar(UTexture2D* NewAvatar)
{
    if (HasAuthority())
    {
        Info.Avatar = NewAvatar;
        OnRep_Info(); // Manually call for server
    }
}

UTexture2D* AMyPlayerState::GetPlayerAvatar() const
{
    return Info.Avatar.Get();
}

void AMyPlayerState::SetCharacterDataRow(const FName& NewCharacterDataRow)
{
    if (HasAuthority())
    {
        Info.CharacterDataRow = NewCharacterDataRow;
        OnRep_Info(); // Manually call for server
    }
}

FName AMyPlayerState::GetCharacterDataRow() const
{
    return Info.CharacterDataRow;
}

void AMyPlayerState::SetPlayerReady(bool bNewReady)
{
    if (HasAuthority())
    {
        Info.IsReady = bNewReady;
        OnRep_Info(); // Manually call for server
    }
}

bool AMyPlayerState::GetPlayerReady() const
{
    return Info.IsReady;
}
