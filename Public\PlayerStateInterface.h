// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "PlayerTypes.h"
#include "PlayerStateInterface.generated.h"

// This class does not need to be modified.
UINTERFACE(MinimalAPI, BlueprintType)
class UPlayerStateInterface : public UInterface
{
	GENERATED_BODY()
};

/**
 * Blueprint Interface for PlayerState functionality
 * Provides methods for setting and getting player info and team
 */
class NEWGAME_API IPlayerStateInterface
{
	GENERATED_BODY()

	// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	
	// Player Info functions
	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual void SetPlayerInfo(const FPlayerInfo& NewInfo) = 0;

	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual FPlayerInfo GetPlayerInfo() const = 0;

	// Player Team functions
	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual void SetPlayerTeam(EPlayerTeam NewTeam) = 0;

	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual EPlayerTeam GetPlayerTeam() const = 0;

	// Modular Player Info property functions
	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual void SetPlayerName(const FText& NewName) = 0;

	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual FText GetPlayerName() const = 0;

	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual void SetPlayerAvatar(UTexture2D* NewAvatar) = 0;

	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual UTexture2D* GetPlayerAvatar() const = 0;

	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual void SetCharacterDataRow(const FName& NewCharacterDataRow) = 0;

	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual FName GetCharacterDataRow() const = 0;

	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual void SetPlayerReady(bool bNewReady) = 0;

	UFUNCTION(BlueprintCallable, Category = "Player State Interface")
	virtual bool GetPlayerReady() const = 0;
};
