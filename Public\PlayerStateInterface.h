// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "UObject/Interface.h"
#include "PlayerTypes.h"
#include "PlayerStateInterface.generated.h"

// This class does not need to be modified.
UINTERFACE(MinimalAPI, BlueprintType)
class UPlayerStateInterface : public UInterface
{
	GENERATED_BODY()
};

/**
 * Blueprint Interface for PlayerState functionality
 * Provides methods for setting and getting player info and team
 */
class NEWGAME_API IPlayerStateInterface
{
	GENERATED_BODY()

	// Add interface functions to this class. This is the class that will be inherited to implement this interface.
public:
	
	// Player Info functions
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	void SetPlayerInfo(const FPlayerInfo& NewInfo);
	
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	FPlayerInfo GetPlayerInfo() const;
	
	// Player Team functions
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	void SetPlayerTeam(EPlayerTeam NewTeam);
	
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	EPlayerTeam GetPlayerTeam() const;

	// Modular Player Info property functions
	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	void SetPlayerName(const FText& NewName);

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	FText GetPlayerName() const;

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	void SetPlayerAvatar(UTexture2D* NewAvatar);

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	UTexture2D* GetPlayerAvatar() const;

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	void SetCharacterDataRow(const FName& NewCharacterDataRow);

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	FName GetCharacterDataRow() const;

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	void SetPlayerReady(bool bNewReady);

	UFUNCTION(BlueprintImplementableEvent, BlueprintCallable, Category = "Player State Interface")
	bool GetPlayerReady() const;
};
