// Fill out your copyright notice in the Description page of Project Settings.


#include "MultiplayerPlayerController.h"
#include "OnlineSubsystem.h"
#include "OnlineSessionSettings.h"
#include "Interfaces/OnlineSessionInterface.h"

//#include "Engine/GameInstance.h"
#include "Engine/World.h"
//#include "Online.h"
#include "IPAddress.h"

FString AMultiplayerPlayerController::GetIP()
{
    if (NetConnection) // This is a field of PlayerController
    {
        ip = *NetConnection->URL.Host;
        GEngine->AddOnScreenDebugMessage(-1, 10.f, FColor::Red, FString::Printf(TEXT("Net request url is %s"), *NetConnection->URL.Host));
    }
    else
    {
        UWorld* World = GetWorld();
        UNetDriver* NetDriver = World->GetNetDriver();
        TSharedPtr<const FInternetAddr> LocalAddress = NetDriver->GetLocalAddr();
        ip = LocalAddress->ToString(false);
        
        GEngine->AddOnScreenDebugMessage(-1, 10.f, FColor::Red, FString::Printf(TEXT("NetConnection is null")));
    }
    return ip;
}

void AMultiplayerPlayerController::AcknowledgePossession(APawn* P)
{
    Super::AcknowledgePossession(P);
    OnAcknowledgePossession(P);
}

ULocalPlayer* AMultiplayerPlayerController::GetLocalPlayerFromBP() const
{
    return Cast<ULocalPlayer>(Player);
}

void AMultiplayerPlayerController::PreClientTravel(const FString& PendingURL, ETravelType TravelType, bool bIsSeamlessTravel)
{
    Super::PreClientTravel(PendingURL, TravelType, bIsSeamlessTravel);
    OnPreClientTravel(PendingURL, TravelType, bIsSeamlessTravel);
}
