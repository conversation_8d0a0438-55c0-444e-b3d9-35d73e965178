// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/LocalPlayer.h"
#include "MyLocalPlayer.generated.h"

/**
 * 
 */
UCLASS(BlueprintType)
class NEWGAME_API UMyLocalPlayer : public ULocalPlayer
{
	GENERATED_BODY()
	
	virtual FString GetGameLoginOptions() const override;

public:
	UPROPERTY(VisibleAnywhere, BlueprintReadWrite)
	FString GameLoginOptions;
};
