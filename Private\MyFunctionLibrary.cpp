#include "MyFunctionLibrary.h"
#include "Blueprint/UserWidget.h"
#include <Slate/WidgetRenderer.h>
#include "Engine/TextureRenderTarget2D.h"

UAISenseConfig* UFunctionLibrary::GetPerceptionSenseConfig(UAIPerceptionComponent* Perception, TSubclassOf<UAISense> SenseClass)
{
    UAISenseConfig* result = nullptr;
    FAISenseID Id = UAISense::GetSenseID(SenseClass);
    if (!Id.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("GetPerceptionSenseConfig: Wrong Sense ID"));
    }
    else if (Perception == nullptr)
    {
        UE_LOG(LogTemp, Error, TEXT("GetPerceptionSenseConfig: Perception == nullptr"));
    }
    else
    {
        result = Perception->GetSenseConfig(Id);
    }

    return result;
}

bool UFunctionLibrary::SetSightRange(UAIPerceptionComponent* Perception, float SightRange)
{
    UAISenseConfig* Config = GetPerceptionSenseConfig(Perception, UAISense_Sight::StaticClass());
    if (Config == nullptr)
    {
        UE_LOG(LogTemp, Error, TEXT("SetSightRange: Config == nullptr"));
        return false;
    }
    else
    {
        UAISenseConfig_Sight* ConfigSight = Cast<UAISenseConfig_Sight>(Config);

        UE_LOG(LogTemp, Verbose, TEXT("SetSightRange was %f %f, setting to %f %f")
            , ConfigSight->SightRadius, ConfigSight->LoseSightRadius, SightRange, (ConfigSight->LoseSightRadius - ConfigSight->SightRadius + SightRange));

        // Save original lose range
        float LoseRange = ConfigSight->LoseSightRadius - ConfigSight->SightRadius;
        ConfigSight->SightRadius = SightRange;
        // Apply lose range to new radius of the sight
        ConfigSight->LoseSightRadius = ConfigSight->SightRadius + LoseRange;
        Perception->RequestStimuliListenerUpdate();
    }
    return true;
}

bool UFunctionLibrary::SetVisionAngle(UAIPerceptionComponent* Perception, float VisionAngle)
{
    UAISenseConfig* Config = GetPerceptionSenseConfig(Perception, UAISense_Sight::StaticClass());
    if (Config == nullptr)
    {
        UE_LOG(LogTemp, Error, TEXT("SetSightRange: Config == nullptr"));
        return false;
    }
    else
    {
        UAISenseConfig_Sight* ConfigSight = Cast<UAISenseConfig_Sight>(Config);

        VisionAngle = VisionAngle / 2.0f;

        UE_LOG(LogTemp, Verbose, TEXT("SetVisionAngle was %f, setting to %f")
            , ConfigSight->PeripheralVisionAngleDegrees, VisionAngle);

        ConfigSight->PeripheralVisionAngleDegrees = VisionAngle;
        Perception->RequestStimuliListenerUpdate();
    }
    return true;
}

UTextureRenderTarget2D* UFunctionLibrary::CreateWidgetRenderTarget(const FVector2D& drawSize)
{
    return FWidgetRenderer::CreateTargetFor(drawSize, TF_Bilinear, false);
}

UTextureRenderTarget2D* UFunctionLibrary::WidgetToTexture(UUserWidget* const widget, UTextureRenderTarget2D* const renderTarget)
{
    // As long as the slate application is initialized and the widget passed in is not null continue...
    if (FSlateApplication::IsInitialized() && widget != nullptr)
    {
        // Get the slate widget as a smart pointer. Return if null.
        TSharedPtr<SWidget> SlateWidget(widget->TakeWidget());
        if (!SlateWidget) return nullptr;
        // Create a new widget renderer to render the widget to a texture render target 2D.
        static FWidgetRenderer* WidgetRenderer = new FWidgetRenderer(true);
        if (!WidgetRenderer) return nullptr;
        // Update the render target 2D.
        FVector2D drawSize(renderTarget->SizeX, renderTarget->SizeY);
        WidgetRenderer->DrawWidget(renderTarget, SlateWidget.ToSharedRef(), drawSize, 0, false);
        // Return the updated render target 2D.
        return renderTarget;
    }
    else return nullptr;
}
