// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/GameViewportClient.h"
#include "MyGameViewportClient.generated.h"

/**
 * 
 */
UCLASS()
class NEWGAME_API UMyGameViewportClient : public UGameViewportClient
{
	GENERATED_BODY()

	virtual bool HideCursorDuringCapture() const override;

	UFUNCTION(BlueprintCallable)
	void TestMe(const FPointerEvent& InMouseEvent);
};
