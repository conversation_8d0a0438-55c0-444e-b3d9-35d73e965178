


#include "BaseCharacter.h"

// Sets default values
ABaseCharacter::<PERSON><PERSON><PERSON><PERSON><PERSON>()
{
 	// Set this character to call <PERSON><PERSON>() every frame.  You can turn this off to improve performance if you don't need it.
	PrimaryActorTick.bCanEverTick = true;

}

// Called when the game starts or when spawned
void ABase<PERSON>haracter::BeginPlay()
{
	Super::BeginPlay();

}

// Called every frame
void ABase<PERSON>haracter::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

}

// Called to bind functionality to input
void ABaseCharacter::SetupPlayerInputComponent(UInputComponent* PlayerInputComponent)
{
	Super::SetupPlayerInputComponent(PlayerInputComponent);

}

// Use character's head location for AI Perception
void ABaseCharacter::GetActorEyesViewPoint(FVector& Location, FRotator& Rotation) const
{
	Location = GetMesh()->GetSocketLocation("head");

	Rotation = GetActorRotation();
	Rotation.Yaw -= GetMesh()->GetSocketTransform("head", RTS_ParentBoneSpace).Rotator().Roll;
}