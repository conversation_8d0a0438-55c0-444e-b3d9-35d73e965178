// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Texture2D.h"
#include "PlayerTypes.generated.h"

/** Please add a struct description */
USTRUCT(BlueprintType)
struct NEWGAME_API FPlayerInfo
{
	GENERATED_BODY()
public:
	/** Please add a variable description */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(DisplayName="Name"))
	FText Name;

	/** Please add a variable description */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(DisplayName="Avatar", MakeStructureDefaultValue="None"))
	TObjectPtr<UTexture2D> Avatar;

	/** Please add a variable description */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(DisplayName="CharacterDataRow", MakeStructureDefaultValue="None"))
	FName CharacterDataRow;

	/** Please add a variable description */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(DisplayName="IsReady"))
	bool IsReady;
};

UENUM(BlueprintType)
enum class EPlayerTeam : uint8
{
	PT_RED	UMETA(DisplayName="Red"),
	PT_BLUE	UMETA(DisplayName="Blue")
};
