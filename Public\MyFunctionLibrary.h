
#pragma once

#include "Kismet/BlueprintFunctionLibrary.h"
#include "Perception/AIPerceptionComponent.h"
#include "Perception/AISense.h"
#include "Perception/AISenseConfig.h"
#include "Perception/AISenseConfig_Sight.h"
#include "Perception/AISenseConfig_Hearing.h"
#include "MyFunctionLibrary.generated.h"

UCLASS()
class UFunctionLibrary : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()
	
public:
	//static UAISenseConfig* GetPerceptionSenseConfig(AAIController* Controller, TSubclassOf<UAISense> SenseClass);
	static UAISenseConfig* GetPerceptionSenseConfig(UAIPerceptionComponent* Perception, TSubclassOf<UAISense> SenseClass);

	UFUNCTION(BlueprintCallable)
	static bool SetSightRange(UAIPerceptionComponent* Perception, float SightRange);
	
	//Set a new Peripheral Vision Angle for the AI Perception Component's Sight Config.
	UFUNCTION(BlueprintCallable)
	static bool SetVisionAngle(UAIPerceptionComponent* Perception, float VisionAngle);

	/**
	* Renders a widget to a Render Texture 2D with the given draw size.
	*
	* @param widget      The widget to converted to a Render Texture 2D.
	* @param drawSize    The size to render the Widget to. Also will be the texture size.
	* @return            The texture render target 2D containing the rendered widget.
	*/
	UFUNCTION(BlueprintCallable, Category = RenderTexture)
	static class UTextureRenderTarget2D* WidgetToTexture(class UUserWidget* const widget, class UTextureRenderTarget2D* const renderTarget);

	UFUNCTION(BlueprintCallable, Category = RenderTexture)
	static class UTextureRenderTarget2D* CreateWidgetRenderTarget(const FVector2D& drawSize);

};