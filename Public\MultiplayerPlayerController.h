// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerController.h"
#include "MultiplayerPlayerController.generated.h"

/**
 * 
 */
UCLASS(Blueprintable, BlueprintType)
class NEWGAME_API AMultiplayerPlayerController : public APlayerController
{
	GENERATED_BODY()

public:
	UFUNCTION(BlueprintCallable, Category = "Multiplayer")
	FString GetIP();

	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Multiplayer")
	FString ip;

	virtual void AcknowledgePossession(class APawn* P) override;

	UFUNCTION(BlueprintImplementableEvent)
	void OnAcknowledgePossession(APawn* NewPawn);

	UFUNCTION(BlueprintCallable, Category = "LocalPlayer")
	class ULocalPlayer* GetLocalPlayerFromBP() const;

	/**
	* Called when the local player is about to travel to a new map or IP address.  Provides subclass with an opportunity
	* to perform cleanup or other tasks prior to the travel.
	*/
	virtual void PreClientTravel(const FString& PendingURL, ETravelType TravelType, bool bIsSeamlessTravel) override;

	UFUNCTION(BlueprintImplementableEvent)
	void OnPreClientTravel(const FString& PendingURL, ETravelType TravelType, bool bIsSeamlessTravel);
};
