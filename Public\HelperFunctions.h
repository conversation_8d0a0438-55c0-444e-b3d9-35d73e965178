// Fill out your copyright notice in the Description page of Project Settings.
// Paste some code from: Copyright Redpoint Games Pty Ltd 2017 All Rights Reserved. Some portions of this code are from <PERSON>'s Victory BP Library, which is licensed separately.
#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"

#include "Components/SceneComponent.h"
#include "Engine/LevelStreamingDynamic.h"
#include "Engine/LevelStreaming.h"

#include "HelperFunctions.generated.h"

UCLASS()
class NEWGAME_API UHelperFunctions : public UBlueprintFunctionLibrary
{
	GENERATED_BODY()

	UFUNCTION(BlueprintPure, Category = "Level Streaming")
	static void GetAllActorsOfClassFromStreamingLevel(TSubclassOf<AActor> ActorClass, ULevelStreaming* StreamingLevel, TArray<AActor*>& OutActors);

	UFUNCTION(BlueprintPure, Category = "Level Streaming")
	static ULevelStreaming* GetStreamingLevelFromActor(AActor* Actor);

	UFUNCTION(BlueprintPure, Category = "Level Streaming")
	static TArray<ULevelStreaming*> GetStreamedLevels(UObject* WorldContextObject);

	/** Replicated function to set the pawn rotation, allowing the server to force. */
	UFUNCTION(Category = "Controller", BlueprintCallable)
	static void ClientSetRotation(AController* Controller, FRotator NewRotation, bool bResetCamera = false);
};