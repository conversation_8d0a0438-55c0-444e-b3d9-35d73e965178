// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerState.h"
#include "PlayerTypes.h"
#include "PlayerStateInterface.h"
#include "MyPlayerState.generated.h"

/**
 * 
 */
UCLASS()
class NEWGAME_API AMyPlayerState : public APlayerState, public IPlayerStateInterface
{
    GENERATED_BODY()
    
public:
    AMyPlayerState();

    // Called when Info is replicated
    UFUNCTION()
    void OnRep_Info();

    // Called when Team is replicated
    UFUNCTION()
    void OnRep_Team();

    // Blueprint event to update UI
    UFUNCTION(BlueprintImplementableEvent, Category="PlayerState")
    void OnPlayerStateUpdated();

    // Interface implementations
    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual void SetPlayerInfo(const FPlayerInfo& NewInfo) override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual FPlayerInfo GetPlayerInfo() const override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual void SetPlayerTeam(EPlayerTeam NewTeam) override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual EPlayerTeam GetPlayerTeam() const override;

    // Modular Player Info property functions
    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual void SetPlayerName(const FText& NewName) override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual FText GetPlayerName() const override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual void SetPlayerAvatar(UTexture2D* NewAvatar) override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual UTexture2D* GetPlayerAvatar() const override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual void SetCharacterDataRow(const FName& NewCharacterDataRow) override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual FName GetCharacterDataRow() const override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual void SetPlayerReady(bool bNewReady) override;

    UFUNCTION(BlueprintCallable, Category = "Player State Interface")
    virtual bool GetPlayerReady() const override;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, ReplicatedUsing=OnRep_Info)
    FPlayerInfo Info;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, ReplicatedUsing=OnRep_Team)
    EPlayerTeam Team;

    // Replication setup
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
};