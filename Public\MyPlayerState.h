// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/PlayerState.h"
#include "MyPlayerState.generated.h"

/** Please add a struct description */
USTRUCT(BlueprintType)
struct FPlayerInfo
{
	GENERATED_BODY()
public:
	/** Please add a variable description */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(DisplayName="Name"))
	FText Name;

	/** Please add a variable description */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(DisplayName="Avatar", MakeStructureDefaultValue="None"))
	TObjectPtr<UTexture2D> Avatar;

	/** Please add a variable description */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(DisplayName="CharacterDataRow", MakeStructureDefaultValue="None"))
	FName CharacterDataRow;

	/** Please add a variable description */
	UPROPERTY(BlueprintReadWrite, EditAnywhere, meta=(DisplayName="IsReady"))
	bool IsReady;
};

UENUM(BlueprintType)
enum class EPlayerTeam : uint8
{
	PT_RED	UMETA(DisplayName="Red"),
	PT_BLUE	UMETA(DisplayName="Blue")
};

/**
 * 
 */
UCLASS()
class NEWGAME_API AMyPlayerState : public APlayerState
{
    GENERATED_BODY()
    
public:
    AMyPlayerState();

    // Called when Info is replicated
    UFUNCTION()
    void OnRep_Info();

    // Called when Team is replicated
    UFUNCTION()
    void OnRep_Team();

    // Blueprint event to update UI
    UFUNCTION(BlueprintImplementableEvent, Category="PlayerState")
    void OnPlayerStateUpdated();

    UPROPERTY(BlueprintReadWrite, EditAnywhere, ReplicatedUsing=OnRep_Info)
    FPlayerInfo Info;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, ReplicatedUsing=OnRep_Team)
    EPlayerTeam Team;

    // Replication setup
    virtual void GetLifetimeReplicatedProps(TArray<FLifetimeProperty>& OutLifetimeProps) const override;
};