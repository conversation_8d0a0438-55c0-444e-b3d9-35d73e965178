// Fill out your copyright notice in the Description page of Project Settings.

#include "HelperFunctions.h"
#include "Net/UnrealNetwork.h"
#include "Engine/World.h"
#include "Engine/LevelBounds.h"
#include "GameFramework/Controller.h"

void UHelperFunctions::GetAllActorsOfClassFromStreamingLevel(TSubclassOf<AActor> ActorClass, ULevelStreaming* StreamingLevel, TArray<AActor*>& OutActors)
{
	OutActors.Reset();

	if (ActorClass && StreamingLevel)
	{
		ULevel* Level = StreamingLevel->GetLoadedLevel();
		if (Level)
		{
			OutActors.Reserve(Level->Actors.Num());
			for (const auto LevelActor : Level->Actors)
			{
				if (IsValid(LevelActor) && LevelActor->IsA(ActorClass))
				{
					OutActors.Add(LevelActor);
				}
			}
		}
	}
}

ULevelStreaming* UHelperFunctions::GetStreamingLevelFromActor(AActor* Actor)
{
	if (Actor != nullptr)
	{
		ULevel* Level = Actor->GetLevel();
		if (Level)
		{
			return (ULevelStreaming*)Level->GetOuter();
		}
	}
	return nullptr;
}

TArray<ULevelStreaming*> UHelperFunctions::GetStreamedLevels(UObject* WorldContextObject) {
	UWorld* World = WorldContextObject->GetWorld();
	if (World)
	{
		return World->GetStreamingLevels();
	}

	TArray<ULevelStreaming*> emptyArray;
	return emptyArray;
}

void UHelperFunctions::ClientSetRotation(AController* Controller, FRotator NewRotation, bool bResetCamera)
{
	if (Controller)
	{
		Controller->ClientSetRotation(NewRotation, bResetCamera);
	}
}